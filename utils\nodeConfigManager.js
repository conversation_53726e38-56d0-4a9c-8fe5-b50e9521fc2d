import { PATH, UUID, SECRET, AESKEY, SYSTEM } from '@/utils/config';
import { getServerList } from '@/api/serverList';

/**
 * 节点配置管理工具
 * 用于处理节点访问时的配置切换和恢复
 */

/**
 * 保存当前面板配置
 */
export const saveCurrentPanelConfig = () => {
	const currentConfig = {
		path: PATH.value,
		uuid: UUID.value,
		secret: SECRET.value,
		aeskey: AESKEY.value,
		system: SYSTEM.value,
	};
	uni.setStorageSync('tempPanelConfig', currentConfig);
	console.log('已保存当前面板配置:', currentConfig);
};

/**
 * 恢复原面板配置
 */
export const restoreOriginalPanelConfig = () => {
	const savedConfig = uni.getStorageSync('tempPanelConfig');
	if (savedConfig) {
		PATH.value = savedConfig.path;
		UUID.value = savedConfig.uuid;
		SECRET.value = savedConfig.secret;
		AESKEY.value = savedConfig.aeskey;
		SYSTEM.value = savedConfig.system;

		uni.removeStorageSync('tempPanelConfig');
		console.log('已恢复原面板配置:', savedConfig);
	}
};

/**
 * 设置节点访问标记
 * @param {boolean} flag - 是否从节点访问
 * @param {string} nodeId - 节点ID
 */
export const setNodeAccessFlag = (flag, nodeId = null) => {
	uni.setStorageSync('isFromNodeAccess', flag);
	if (nodeId) {
		uni.setStorageSync('currentNodeId', nodeId);
	}
};

/**
 * 检查是否从节点访问
 * @returns {boolean}
 */
export const isFromNodeAccess = () => {
	return uni.getStorageSync('isFromNodeAccess') || false;
};

/**
 * 清除节点访问标记
 */
export const clearNodeAccessFlag = () => {
	uni.removeStorageSync('isFromNodeAccess');
	uni.removeStorageSync('currentNodeId');
};

/**
 * 生成随机UUID
 * @returns {string}
 */
export const generateUUID = () => {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		const r = (Math.random() * 16) | 0;
		const v = c === 'x' ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
};

/**
 * 生成随机SECRET
 * @returns {string}
 */
export const generateSecret = () => {
	return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

/**
 * 检测系统类型
 * @param {Object} systemData - 系统信息数据
 * @returns {string} 'linux' 或 'windows'
 */
export const detectSystemType = (systemData) => {
	console.log(systemData);
	if (systemData && systemData.system && systemData.system.toLowerCase().includes('linux')) {
		return 'linux';
	}
	return 'windows';
};

/**
 * 设置节点配置
 * @param {Object} nodeData - 节点数据
 */
export const setupNodeConfig = async (nodeData) => {
	try {
		// 1. 设置基本配置
		PATH.value = nodeData.address; // 来自接口的 address
		SECRET.value = nodeData.api_key; // 来自接口的 api_key
		UUID.value = generateUUID(); // 随机生成
		AESKEY.value = generateSecret(); // 随机生成

		console.log('设置节点基本配置:', {
			path: PATH.value,
			aeskey: AESKEY.value,
			uuid: UUID.value,
			secret: SECRET.value,
		});

		// 2. 检测系统类型
		try {
			const systemRes = await getServerList(); // 调用 /system?action=GetNetWork
			SYSTEM.value = detectSystemType(systemRes);
			console.log('检测到系统类型:', SYSTEM.value);
		} catch (error) {
			console.error('检测系统类型失败:', error);
			SYSTEM.value = 'linux'; // 默认为 linux
			console.log('使用默认系统类型:', SYSTEM.value);
		}
	} catch (error) {
		console.error('设置节点配置失败:', error);
		throw error;
	}
};

/**
 * 获取当前节点ID
 * @returns {string|null}
 */
export const getCurrentNodeId = () => {
	return uni.getStorageSync('currentNodeId') || null;
};

/**
 * 安全恢复配置（带错误处理）
 */
export const safeRestoreConfig = () => {
	try {
		if (isFromNodeAccess()) {
			restoreOriginalPanelConfig();
			clearNodeAccessFlag();
		}
	} catch (error) {
		console.error('恢复配置时出错:', error);
		// 即使出错也要清除标记，避免状态不一致
		clearNodeAccessFlag();
	}
};
